// === SCENE MANAGEMENT SYSTEM ===
// Moved from manager.js to keep it organized

// Capture current scene state
function getCurrentSceneState() {
  // Get projection object and extract name
  let currentProjection;
  let projectionName;
  try {
    currentProjection = map.getProjection?.() || map.projection;
    if (currentProjection) {
      projectionName = currentProjection.name || currentProjection.type || JSON.stringify(currentProjection);
      console.log(`💾 Full projection object:`, currentProjection);
      console.log(`💾 Projection properties:`, Object.keys(currentProjection));
    } else {
      projectionName = 'mercator';
    }
  } catch (e) {
    projectionName = 'mercator'; // fallback
  }
  
  console.log(`💾 Saving scene with projection name: ${projectionName}`);
  
  // Get wind settings for debugging
  const windSettings = window.windControlsManager ? window.windControlsManager.getCurrentSettings() : null;
  if (windSettings) {
    console.log(`💨 Capturing wind settings:`, windSettings);
  }
  
  return {
    // Timeline
    time: parseInt(timeSlider.value),
    
    // Viewport
    center: map.getCenter(),
    zoom: map.getZoom(),
    bearing: map.getBearing(),
    pitch: map.getPitch(),
    
    // Layers
    tempLayerVisible: temperatureControlsManager ? temperatureControlsManager.tempLayerVisible : true,
    hillshadeVisible: hillshadeVisible,
    isPlaying: isPlaying,
    
    // Additional state
    projection: projectionName,
    projectionObject: currentProjection, // Store full object too
    
    // Wind Settings (simplified)
    windSettings: windSettings,
    
    // Timestamp
    savedAt: new Date().toISOString()
  };
}

// Save scene to specific slot
function saveScene(sceneIndex, sceneState) {
  scenes[sceneIndex] = sceneState;
  
  // Update button appearance
  const button = document.getElementById(`scene-${sceneIndex}`);
  if (button) {
    button.classList.add('saved');
    button.title = `Saved: ${new Date(sceneState.savedAt).toLocaleString()}`;
  }
  
  // Save to localStorage for persistence
  localStorage.setItem('weather-app-scenes', JSON.stringify(scenes));
  
  // Add timeline marker for localStorage save
  if (window.timelineManager) {
    window.timelineManager.addSceneMarker(
      `scene-${sceneIndex}`,
      'localStorage',
      sceneState.time,
      `Scene ${sceneIndex}`
    );
  }
  
  console.log(`💾 Scene ${sceneIndex} saved:`, sceneState);
}

// Scene loading debounce protection
let sceneLoadingTimeout = null;
let isLoadingScene = false;

// Compare two scene states to see if they match
function compareSceneStates(current, target) {
  // Define tolerance for floating point comparison
  const tolerance = 0.001;
  
  // Compare viewport
  if (Math.abs(current.center[0] - target.center[0]) > tolerance ||
      Math.abs(current.center[1] - target.center[1]) > tolerance ||
      Math.abs(current.zoom - target.zoom) > tolerance ||
      Math.abs((current.bearing || 0) - (target.bearing || 0)) > tolerance ||
      Math.abs((current.pitch || 0) - (target.pitch || 0)) > tolerance) {
    console.log(`🔍 Viewport mismatch:`, {
      current: { center: current.center, zoom: current.zoom, bearing: current.bearing, pitch: current.pitch },
      target: { center: target.center, zoom: target.zoom, bearing: target.bearing, pitch: target.pitch }
    });
    return false;
  }
  
  // Compare timeline (within 5 seconds tolerance)
  if (Math.abs(current.time - target.time) > 5000) {
    console.log(`⏰ Timeline mismatch:`, {
      current: new Date(current.time),
      target: new Date(target.time),
      diff: Math.abs(current.time - target.time) / 1000 + 's'
    });
    return false;
  }
  
  // Compare layer visibility
  if (current.tempLayerVisible !== target.tempLayerVisible ||
      current.hillshadeVisible !== target.hillshadeVisible ||
      current.isPlaying !== target.isPlaying) {
    console.log(`👁️ Layer visibility mismatch:`, {
      current: { temp: current.tempLayerVisible, hillshade: current.hillshadeVisible, playing: current.isPlaying },
      target: { temp: target.tempLayerVisible, hillshade: target.hillshadeVisible, playing: target.isPlaying }
    });
    return false;
  }
  
  // Compare projection
  if ((current.projection || 'mercator') !== (target.projection || 'mercator')) {
    console.log(`🗺️ Projection mismatch:`, {
      current: current.projection || 'mercator',
      target: target.projection || 'mercator'
    });
    return false;
  }
  
  // Compare wind settings
  if (current.windSettings && target.windSettings) {
    const currentWind = current.windSettings;
    const targetWind = target.windSettings;
    
    if (currentWind.visible !== targetWind.visible) {
      console.log(`💨 Wind visibility mismatch:`, {
        current: currentWind.visible,
        target: targetWind.visible
      });
      return false;
    }
    
    // Compare colors (allow small differences due to rounding)
    const colorTolerance = 2;
    if (currentWind.slowColor && targetWind.slowColor) {
      for (let i = 0; i < 4; i++) {
        if (Math.abs((currentWind.slowColor[i] || 0) - (targetWind.slowColor[i] || 0)) > colorTolerance) {
          console.log(`💨 Wind slow color mismatch:`, {
            current: currentWind.slowColor,
            target: targetWind.slowColor
          });
          return false;
        }
      }
    }
    
    if (currentWind.fastColor && targetWind.fastColor) {
      for (let i = 0; i < 4; i++) {
        if (Math.abs((currentWind.fastColor[i] || 0) - (targetWind.fastColor[i] || 0)) > colorTolerance) {
          console.log(`💨 Wind fast color mismatch:`, {
            current: currentWind.fastColor,
            target: targetWind.fastColor
          });
          return false;
        }
      }
    }
  } else if (current.windSettings || target.windSettings) {
    console.log(`💨 Wind settings existence mismatch:`, {
      current: !!current.windSettings,
      target: !!target.windSettings
    });
    return false;
  }
  
  console.log(`✅ Scene states match completely`);
  return true;
}

// Load scene from specific slot (check both localStorage and IndexedDB)
async function loadScene(sceneIndex) {
  // Prevent multiple rapid clicks
  if (isLoadingScene) {
    console.log(`⏳ Scene loading in progress, ignoring click`);
    return;
  }

  // Check if current state matches the scene to load
  let shouldSkipLoad = false;
  if (currentSceneIndex === sceneIndex) {
    // Compare current state with scene state
    const currentState = getCurrentSceneState();

    // Try localStorage first for comparison
    let targetScene = scenes[sceneIndex];

    // If not in localStorage, try IndexedDB
    if (!targetScene && window.offlineSceneManager) {
      targetScene = await window.offlineSceneManager.loadScene(`scene-${sceneIndex}`);
    }

    if (targetScene) {
      const stateMatches = compareSceneStates(currentState, targetScene);
      if (stateMatches) {
        console.log(`📍 Already at Scene ${sceneIndex} with matching state, ignoring click`);
        shouldSkipLoad = true;
      } else {
        console.log(`🔄 At Scene ${sceneIndex} but state differs, reloading...`);
      }
    }
  }

  if (shouldSkipLoad) {
    return;
  }

  // Try localStorage first
  let sceneState = scenes[sceneIndex];

  // If not found in localStorage, try IndexedDB
  if (!sceneState && window.offlineSceneManager) {
    console.log(`🔍 Checking IndexedDB for scene-${sceneIndex}...`);
    sceneState = await window.offlineSceneManager.loadScene(`scene-${sceneIndex}`);

    if (sceneState) {
      console.log(`📦 Found scene in IndexedDB:`, sceneState);
    }
  }

  if (!sceneState) {
    console.log(`❌ Scene ${sceneIndex} is empty in both localStorage and IndexedDB`);
    return;
  }

  console.log(`🎬 Loading Scene ${sceneIndex}:`, sceneState);

  // Set loading flag
  isLoadingScene = true;

  // Clear any existing timeout
  if (sceneLoadingTimeout) {
    clearTimeout(sceneLoadingTimeout);
  }

  // Update current scene indicator
  updateActiveSceneButton(sceneIndex);

  // Apply timeline
  if (sceneState.time) {
    timeSlider.value = sceneState.time;
    layer.setAnimationTime(sceneState.time / 1000);
    const tempLayer = temperatureControlsManager.getTemperatureLayer();
    if (tempLayer) tempLayer.setAnimationTime(sceneState.time / 1000);

    refreshTimeUI();
  }

  // Apply viewport
  map.easeTo({
    center: sceneState.center,
    zoom: sceneState.zoom,
    bearing: sceneState.bearing || 0,
    pitch: sceneState.pitch || 0,
    duration: 1500
  });

  // Apply layer visibility
  if (sceneState.tempLayerVisible !== temperatureControlsManager.tempLayerVisible) {
    // Use temperature controls to toggle
    if (temperatureControlsManager) {
      temperatureControlsManager.currentSettings.visible = sceneState.tempLayerVisible;
      if (temperatureControlsManager.onApplyCallback) {
        temperatureControlsManager.onApplyCallback({
          visible: sceneState.tempLayerVisible,
          type: 'visibility'
        });
      }
    }
  }

  if (sceneState.hillshadeVisible !== hillshadeVisible) {
    hillshadeToggleButton.click();
  }

  // Apply playing state
  if (sceneState.isPlaying !== isPlaying) {
    playPauseButton.click();
  }

  // ✅ Apply unit settings if available
  if (sceneState.unitSettings) {
    console.log(`🔧 Restoring unit settings from scene:`, sceneState.unitSettings);

    // Restore temperature unit and update color scale
    if (sceneState.unitSettings.temperature && window.temperatureControlsManager) {
      window.temperatureControlsManager.setUnitState(sceneState.unitSettings.temperature);
      // Force update color scale after unit change
      setTimeout(() => {
        window.temperatureControlsManager.updateColorScale();
      }, 100);
    }

    // Restore wind speed unit
    if (sceneState.unitSettings.windSpeed && window.windControlsManager) {
      window.windControlsManager.setUnitState(sceneState.unitSettings.windSpeed);
    }

    // Restore wind map unit
    if (sceneState.unitSettings.windMap && window.windMapControlsManager) {
      window.windMapControlsManager.setUnitState(sceneState.unitSettings.windMap);
    }
  }

  currentSceneIndex = sceneIndex;

  // Reset loading flag after animation completes
  sceneLoadingTimeout = setTimeout(() => {
    isLoadingScene = false;
    console.log(`✅ Scene ${sceneIndex} loading completed`);
  }, 2000); // Wait for map animation (1500ms) + buffer
}

// Update active scene button indicator
function updateActiveSceneButton(sceneIndex) {
  // Remove active class from all scene buttons
  for (let i = 1; i <= 9; i++) {
    const btn = document.getElementById(`scene-${i}`);
    if (btn) {
      btn.classList.remove('active');
    }
  }

  // Add active class to current scene button
  const activeBtn = document.getElementById(`scene-${sceneIndex}`);
  if (activeBtn) {
    activeBtn.classList.add('active');
  }

  currentSceneIndex = sceneIndex;
}
