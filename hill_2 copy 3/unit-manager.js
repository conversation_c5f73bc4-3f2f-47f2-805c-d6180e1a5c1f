/**
 * Unit Manager - Centralized unit conversion system
 * Handles all unit conversions for weather data (temperature, wind speed, etc.)
 */
class UnitManager {
  constructor() {
    // Supported units for each category
    this.supportedUnits = {
      temperature: ['C', 'F', 'K'],
      windSpeed: ['km/h', 'mph', 'm/s', 'knots'],
      pressure: ['hPa', 'inHg', 'mmHg', 'kPa'],
      precipitation: ['mm', 'in']
    };

    // Default units
    this.defaultUnits = {
      temperature: 'C',
      windSpeed: 'km/h', 
      pressure: 'hPa',
      precipitation: 'mm'
    };

    // Unit display names
    this.unitDisplayNames = {
      // Temperature
      'C': '°C',
      'F': '°F', 
      'K': 'K',
      // Wind Speed
      'km/h': 'km/h',
      'mph': 'mph',
      'm/s': 'm/s',
      'knots': 'kts',
      // Pressure
      'hPa': 'hPa',
      'inHg': 'inHg',
      'mmHg': 'mmHg',
      'kPa': 'kPa',
      // Precipitation
      'mm': 'mm',
      'in': 'in'
    };

    // Event listeners
    this.listeners = {};
  }

  // Validate unit for category
  validateUnit(unit, category) {
    const validUnits = this.supportedUnits[category];
    if (!validUnits || !validUnits.includes(unit)) {
      console.warn(`⚠️ Invalid ${category} unit: ${unit}, using default`);
      return this.defaultUnits[category];
    }
    return unit;
  }

  // Get display name for unit
  getDisplayName(unit) {
    return this.unitDisplayNames[unit] || unit;
  }

  // Get next unit in cycle for UI switching
  getNextUnit(currentUnit, category) {
    const units = this.supportedUnits[category];
    if (!units) return currentUnit;
    
    const currentIndex = units.indexOf(currentUnit);
    const nextIndex = (currentIndex + 1) % units.length;
    return units[nextIndex];
  }

  // Temperature conversions
  convertTemperature(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;
    
    // Convert to Celsius first
    let celsius = value;
    switch (fromUnit) {
      case 'F':
        celsius = (value - 32) * 5/9;
        break;
      case 'K':
        celsius = value - 273.15;
        break;
      case 'C':
        celsius = value;
        break;
      default:
        console.warn(`Unknown temperature unit: ${fromUnit}`);
        return value;
    }
    
    // Convert from Celsius to target unit
    switch (toUnit) {
      case 'C':
        return celsius;
      case 'F':
        return celsius * 9/5 + 32;
      case 'K':
        return celsius + 273.15;
      default:
        console.warn(`Unknown temperature unit: ${toUnit}`);
        return celsius;
    }
  }

  // Wind speed conversions
  convertWindSpeed(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;
    
    // Convert to m/s first
    let mps = value;
    switch (fromUnit) {
      case 'km/h':
        mps = value / 3.6;
        break;
      case 'mph':
        mps = value * 0.44704;
        break;
      case 'knots':
        mps = value * 0.514444;
        break;
      case 'm/s':
        mps = value;
        break;
      default:
        console.warn(`Unknown wind speed unit: ${fromUnit}`);
        return value;
    }
    
    // Convert from m/s to target unit
    switch (toUnit) {
      case 'm/s':
        return mps;
      case 'km/h':
        return mps * 3.6;
      case 'mph':
        return mps / 0.44704;
      case 'knots':
        return mps / 0.514444;
      default:
        console.warn(`Unknown wind speed unit: ${toUnit}`);
        return mps;
    }
  }

  // Pressure conversions
  convertPressure(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;
    
    // Convert to hPa first
    let hpa = value;
    switch (fromUnit) {
      case 'hPa':
        hpa = value;
        break;
      case 'inHg':
        hpa = value * 33.8639;
        break;
      case 'mmHg':
        hpa = value * 1.33322;
        break;
      case 'kPa':
        hpa = value * 10;
        break;
      default:
        console.warn(`Unknown pressure unit: ${fromUnit}`);
        return value;
    }
    
    // Convert from hPa to target unit
    switch (toUnit) {
      case 'hPa':
        return hpa;
      case 'inHg':
        return hpa / 33.8639;
      case 'mmHg':
        return hpa / 1.33322;
      case 'kPa':
        return hpa / 10;
      default:
        console.warn(`Unknown pressure unit: ${toUnit}`);
        return hpa;
    }
  }

  // Precipitation conversions
  convertPrecipitation(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;
    
    switch (fromUnit + '_to_' + toUnit) {
      case 'mm_to_in':
        return value / 25.4;
      case 'in_to_mm':
        return value * 25.4;
      default:
        console.warn(`Unknown precipitation conversion: ${fromUnit} to ${toUnit}`);
        return value;
    }
  }

  // Main conversion function
  convert(value, fromUnit, toUnit, category) {
    if (value === null || value === undefined) return value;
    
    switch (category) {
      case 'temperature':
        return this.convertTemperature(value, fromUnit, toUnit);
      case 'windSpeed':
        return this.convertWindSpeed(value, fromUnit, toUnit);
      case 'pressure':
        return this.convertPressure(value, fromUnit, toUnit);
      case 'precipitation':
        return this.convertPrecipitation(value, fromUnit, toUnit);
      default:
        console.warn(`Unknown category: ${category}`);
        return value;
    }
  }

  // Event system
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  emit(event, ...args) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(...args));
    }
  }

  // Batch update mode for performance
  setBatchUpdateMode(enabled) {
    this.batchUpdateMode = enabled;
  }

  isBatchUpdateMode() {
    return this.batchUpdateMode || false;
  }
}

// Global instance
window.unitManager = new UnitManager();

console.log('🔧 UnitManager initialized');
