// === BORDER AND BOUNDARY MANAGEMENT ===
// Moved from manager.js to keep it organized

// ===== HÀM CHÍNH ĐỂ ĐẢMBẢO RANH GIỚI LUÔN HIỂN THỊ =====
function ensureBoundariesOnTop() {
    if (!map.isStyleLoaded()) return;

    const layers = map.getStyle().layers;
    // console.log('All layers:', layers.map(l => ({ id: l.id, type: l.type })));
    
    // Tìm TẤT CẢ layer có thể là ranh giới hoặc nhãn
    const boundaryAndLabelLayers = layers.filter(layer => {
        const id = layer.id.toLowerCase();
        const type = layer.type;
        
        // Điều kiện mở rộng để bắt tất cả các loại ranh giới và nhãn
        return (
            // Các layer ranh giới
            id.includes('boundary') || 
            id.includes('admin') || 
            id.includes('border') || 
            id.includes('country') || 
            id.includes('state') || 
            id.includes('province') ||
            id.includes('outline') ||
            id.includes('stroke') ||
            // Các layer nhãn
            type === 'symbol' ||
            id.includes('label') || 
            id.includes('text') || 
            id.includes('place') ||
            id.includes('city') ||
            id.includes('town') ||
            // Các layer line có thể là ranh giới
            (type === 'line' && (
                id.includes('line') || 
                id.includes('admin') || 
                id.includes('boundary')
            ))
        );
    });

    // console.log('Boundary and label layers found:', boundaryAndLabelLayers.map(l => l.id));

    // Di chuyển tất cả lên trên cùng, giữ nguyên thứ tự tương đối
    boundaryAndLabelLayers.forEach(layer => {
        try {
            if (map.getLayer(layer.id)) {
                // Xóa fill color cho các layer fill/polygon
                if (layer.type === 'fill') {
                    map.setPaintProperty(layer.id, 'fill-opacity', 0);
                    map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                    // Đảm bảo stroke hiển thị
                    if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                        map.setPaintProperty(layer.id, 'fill-outline-color', '#666666');
                    }
                    // console.log(`Removed fill from layer ${layer.id}`);
                }
                
                map.moveLayer(layer.id);
                // console.log(`Moved layer ${layer.id} to top`);
            }
        } catch (e) {
            console.log(`Failed to move layer ${layer.id}:`, e.message);
        }
    });
}

// Tạo Hàm riêng để đảm bảo borders luôn ở trên
function ensureAllBordersOnTop() {
    if (!mapLoaded || !map.isStyleLoaded()) return;
    
    setTimeout(() => {
        ensureBoundariesOnTop();
        addCustomBorderLayers();
        console.log('All borders moved to top');
    }, 100);
}

// ===== HÀM THÊM BORDER LAYER RIÊNG =====
function addCustomBorderLayers() {
    if (!map.isStyleLoaded()) return;
    
    try {
        // Thêm source cho ranh giới từ MapTiler Countries
        if (!map.getSource('maptiler-countries')) {
            map.addSource('maptiler-countries', {
                type: 'vector',
                url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
            });
            console.log('Added MapTiler Countries source');
        }

        // 1. Thêm ranh giới quốc gia (admin_level=2)
        if (!map.getLayer('country-boundaries')) {
            map.addLayer({
                id: 'country-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: ['==', 'admin_level', 2],
                paint: {
                    'line-color': '#444444',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        0, 0.5,
                        4, 1,
                        8, 1.5,
                        12, 2
                    ],
                    'line-opacity': 0.8
                }
            });
            console.log('Added country boundaries layer');
        }

        // 2. Thêm ranh giới cấp 1 (admin_level=4 - states/provinces)
        if (!map.getLayer('state-boundaries')) {
            map.addLayer({
                id: 'state-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: ['==', 'admin_level', 4],
                paint: {
                    'line-color': '#666666',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        4, 0.3,
                        8, 0.8,
                        12, 1.2
                    ],
                    'line-opacity': 0.6,
                    'line-dasharray': [2, 2]
                }
            });
            console.log('Added state boundaries layer');
        }

        // 3. Đảm bảo các border layer luôn ở trên cùng
        setTimeout(() => {
            if (map.getLayer('state-boundaries')) {
                map.moveLayer('state-boundaries');
            }
            if (map.getLayer('country-boundaries')) {
                map.moveLayer('country-boundaries');
            }
            // console.log('Moved custom border layers to top');
        }, 100);

    } catch (error) {
        console.error('Error adding custom border layers:', error);
    }
}

// Hàm thêm layer border riêng
function addBorderLayer() {
    // Kiểm tra và thêm các layer border có sẵn của MapTiler
    const borderLayers = ['boundary', 'admin', 'country-border'];
    
    borderLayers.forEach(layerName => {
        try {
            if (map.getLayer(layerName)) {
                // Di chuyển layer border lên trên temp layer
                map.moveLayer(layerName, 'temperature-layer');
                console.log(`Moved border layer ${layerName} above temp layer`);
            }
        } catch (e) {
            console.log(`Could not move border layer ${layerName}:`, e.message);
        }
    });
}

// Initialize border management
function initializeBorderManager() {
    // Setup border management on style load
    map.on('styledata', function() {
        // Always try to ensure borders on top when style loads, regardless of mapLoaded flag
        if (map.isStyleLoaded()) {
            ensureAllBordersOnTop();
        }
    });

    // Also ensure borders when map fully loads
    map.on('load', function() {
        setTimeout(() => {
            ensureAllBordersOnTop();
        }, 500);
    });

    // Additional safety: ensure borders after map becomes idle
    map.on('idle', function() {
        if (map.isStyleLoaded()) {
            ensureAllBordersOnTop();
        }
    });

    console.log('🗺️ Border manager initialized');
}

// Export functions for use in manager.js
window.borderManager = {
    ensureBoundariesOnTop,
    ensureAllBordersOnTop,
    addCustomBorderLayers,
    addBorderLayer,
    initializeBorderManager
};
